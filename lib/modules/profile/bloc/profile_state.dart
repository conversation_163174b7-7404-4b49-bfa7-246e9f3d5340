import 'package:melodyze/core/generic_bloc/states.dart';

class AccountDeletedState extends BlocState {
  const AccountDeletedState();
  @override
  List<Object?> get props => [];
}

class LogOutClickedState extends BlocState {
  const LogOutClickedState();
  @override
  List<Object?> get props => [];
}

class ProfileSnackBarState extends BlocState {
  final String message;
  final bool isError;
  const ProfileSnackBarState({required this.message, this.isError = false});
  @override
  List<Object?> get props => [message, DateTime.now(), isError];
}

class ProfilePhotoUploadingState extends BlocState {
  const ProfilePhotoUploadingState();
  @override
  List<Object?> get props => [];
}

class ProfileUpdatedState extends BlocState {
  const ProfileUpdatedState();
  @override
  List<Object?> get props => [DateTime.now()];
}

