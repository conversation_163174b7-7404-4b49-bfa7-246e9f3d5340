import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/modules/profile/bloc/cubit/profile_draft_selection_cubit.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/modules/profile/ui/widget/profile_recording_item.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/navigation/app_router.dart';

class ProfilePublishedRecordingList extends StatefulWidget {
  final List<RecordingModel> recordings;
  const ProfilePublishedRecordingList({
    super.key,
    required this.recordings,
  });

  @override
  State<ProfilePublishedRecordingList> createState() => _ProfilePublishedRecordingListState();
}

class _ProfilePublishedRecordingListState extends State<ProfilePublishedRecordingList> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16.0, 24.0, 16.0, 0.0),
      child: Column(
        children: [
          Expanded(
            child: ListView.separated(
              itemCount: widget.recordings.length,
              padding: EdgeInsets.only(bottom: 16.0, top: 16.0),
              itemBuilder: (context, index) {
                final recording = widget.recordings[index];
                return Padding(
                  padding: EdgeInsets.only(
                    left: 16.0,
                    bottom: index == widget.recordings.length - 1 ? 120.0 : 0.0,
                  ),
                  child: InkWell(
                    onTap: () {
                      context.pushRoute(VideoPlayerReelRoute(recordings: [recording], showMenubar: true));
                      logger.d('[PublishedRecordingList] Navigation push completed for recording: ${recording.id}');
                    },
                    child: RecordingListItem(
                      type: RecordingListType.publishedRecording,
                      recording: recording,
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 12.0),
                child: ImageLoader.fromAsset(AssetPaths.gradientdivider),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void deactivate() {
    context.read<ProfileDraftSelectionCubit>().onDraftRecordingUnselected();
    super.deactivate();
  }
}
