import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:focused_menu/focused_menu.dart';
import 'package:focused_menu/modals.dart';
import 'package:melodyze/core/ui/molecules/dialoges.dart';
import 'package:melodyze/core/ui/molecules/profile_tile.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/modules/profile/bloc/cubit/profile_draft_selection_cubit.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/modules/profile/bloc/profile_event.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/modules/profile/ui/widget/profile_published_recording_list.dart';

class ProfilePublishedRecordings extends StatefulWidget {
  final bool showCurrentRecording;
  final List<RecordingModel> recordings;

  const ProfilePublishedRecordings({
    super.key,
    this.showCurrentRecording = false,
    required this.recordings,
  });

  @override
  State<ProfilePublishedRecordings> createState() => _ProfilePublishedRecordingsState();
}

class _ProfilePublishedRecordingsState extends State<ProfilePublishedRecordings> {
  static const _aspectRatio = 9 / 16;

  @override
  void initState() {
    super.initState();
    if (widget.showCurrentRecording && widget.recordings.isNotEmpty) {
      context.read<ProfileDraftSelectionCubit>().onDraftRecordingSelected(
            widget.recordings.first,
          );
    }
  }

  // Convert List<RecordingModel> to Map<String, List<RecordingModel>> format
  Map<String, List<RecordingModel>> get _recordingsMap {
    final Map<String, List<RecordingModel>> recordingsMap = {};
    for (final recording in widget.recordings) {
      final key = recording.masterSongId.isNotEmpty ? recording.masterSongId : recording.id;
      if (recordingsMap.containsKey(key)) {
        recordingsMap[key]!.add(recording);
      } else {
        recordingsMap[key] = [recording];
      }
    }
    return recordingsMap;
  }

  @override
  Widget build(BuildContext context) {
    final recordingsMap = _recordingsMap;
    return recordingsMap.isEmpty
        ? const Center(child: Text('No recordings found'))
        : BlocBuilder<ProfileDraftSelectionCubit, ProfileDraftSelectionState>(
            builder: (context, state) {
              return (state.recording != null && recordingsMap[state.recording!.masterSongId.isNotEmpty ? state.recording!.masterSongId : state.recording!.id].isNotNullOrEmpty)
                  ? ProfilePublishedRecordingList(recordings: recordingsMap[state.recording!.masterSongId.isNotEmpty ? state.recording!.masterSongId : state.recording!.id] ?? [])
                  : RefreshIndicator(
                      onRefresh: () async {
                        context.read<ProfileBloc>().add(const LoadRecordingsEvent());
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0),
                        child: GridView.builder(
                            padding: const EdgeInsets.only(bottom: 100, top: 24),
                            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 3,
                              childAspectRatio: _aspectRatio,
                              crossAxisSpacing: 16,
                              mainAxisSpacing: 16,
                            ),
                            itemCount: recordingsMap.length,
                            itemBuilder: (context, index) {
                              final entry = recordingsMap.entries.toList()[index];
                              final item = entry.value.first;
                              final songName = FileUtils.fromSnakeCase(item.title.split('-').first);
                              return FocusedMenuHolder(
                                key: ObjectKey(item.id),
                                menuWidth: MediaQuery.of(context).size.width * 0.40,
                                onPressed: () {},
                                menuItems: [
                                  FocusedMenuItem(
                                    title: Text(
                                      'Delete',
                                      style: AppTextStyles.text16medium.copyWith(
                                        color: AppColors.black010101_75,
                                      ),
                                    ),
                                    trailingIcon: const Icon(Icons.delete),
                                    onPressed: () async {
                                      final result = await showYesNoDialog(context: context, title: 'Delete recording');
                                      if (result && context.mounted) {
                                        context.read<ProfileBloc>().add(DeleteRecordingEvent(item.id));
                                      }
                                    },
                                  ),
                                ],
                                child: ProfileTile(
                                  key: ObjectKey(item.id),
                                  title: songName,
                                  thumbnailPath: item.thumbnailPath,
                                  subTitle1: '${entry.value.length} ${entry.value.length > 1 ? 'recordings' : 'recording'}',
                                  subTitle2: "",
                                  subTitle3: "",
                                  imageAspectRatio: _aspectRatio,
                                  onPressed: () {
                                    context.read<ProfileDraftSelectionCubit>().onDraftRecordingSelected(item);
                                  },
                                ),
                              );
                            }),
                      ),
                    );
            },
          );
  }
}
