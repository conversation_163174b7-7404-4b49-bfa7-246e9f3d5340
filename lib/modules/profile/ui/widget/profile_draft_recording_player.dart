import 'dart:async';
import 'package:flutter/material.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/molecules/dialoges.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/modules/profile/bloc/profile_event.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:just_audio/just_audio.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:auto_route/auto_route.dart';
import 'package:melodyze/core/ui/atom/marquee.dart';
import 'package:melodyze/core/ui/atom/app_slider.dart';
import 'package:melodyze/core/ui/molecules/auto_fade_widget.dart';

@RoutePage()
class ProfileDraftRecordingPlayerScreen extends StatefulWidget {
  final RecordingModel recording;

  const ProfileDraftRecordingPlayerScreen({
    super.key,
    required this.recording,
  });

  @override
  State<ProfileDraftRecordingPlayerScreen> createState() => _ProfileDraftRecordingPlayerScreenState();
}

class _ProfileDraftRecordingPlayerScreenState extends State<ProfileDraftRecordingPlayerScreen> {
  late AudioPlayer _audioPlayer;
  int _playbackPositionMillis = 0;
  int _audioDurationMillis = 0;
  bool _isPlaying = false;
  LyricsData? _lyricsData;
  bool _lyricsLoaded = false;

  String get songtitle => FileUtils.fromSnakeCase(widget.recording.title.split('-').first);
  String get info => "${Config.keyMapShort[widget.recording.scale] ?? widget.recording.scale} · ${widget.recording.tempo} bpm";
  String get genre => widget.recording.genre;
  String get singer => widget.recording.singer.isNotEmpty ? widget.recording.singer : ' ';
  bool get isPublishedRecording => widget.recording.isPublished;
  bool get showSongDetails => widget.recording.feedType != 'direct_upload';

  @override
  void initState() {
    super.initState();
    _initializeAudio();
    _fetchLyrics();
  }

  Future<void> _initializeAudio() async {
    _audioPlayer = AudioPlayer();
    await _audioPlayer.setLoopMode(LoopMode.one);
    await _audioPlayer.setUrl(widget.recording.finalMixedAudioPath);

    _audioPlayer.playerStateStream.listen((state) {
      if (mounted) {
        logger.d('Audio player state changed: playing=${state.playing}, processingState=${state.processingState}');
        setState(() {
          _isPlaying = state.playing;
        });
      }
    });

    _audioPlayer.positionStream.listen((position) {
      if (mounted) {
        setState(() {
          _playbackPositionMillis = position.inMilliseconds;
        });
      }
    });

    _audioPlayer.durationStream.listen((duration) {
      if (mounted && duration != null) {
        setState(() {
          _audioDurationMillis = duration.inMilliseconds;
        });
      }
    });

    unawaited(_audioPlayer.play());
  }

  Future<void> _fetchLyrics() async {
    if (_lyricsLoaded) return;

    try {
      if (widget.recording.lyricsJsonPath.isNotEmpty) {
        final response = await DI().resolve<ApiClient>().get(widget.recording.lyricsJsonPath);
        if (response != null) {
          final lyricsData = LyricsData.fromJson(response);
          if (mounted) {
            setState(() {
              _lyricsData = lyricsData;
              _lyricsLoaded = true;
            });
          }
          return;
        }
      }
    } catch (e) {
      logger.e('Failed to fetch lyrics: $e');
    }
  }

  void _togglePlay() {
    if (_isPlaying) {
      _audioPlayer.pause();
    } else {
      _audioPlayer.play();
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final lyricsList = _lyricsData?.lyrics.data.map((e) => e.text).toList() ?? [];

    // Get user profile information from ProfileBloc via DI
    final profileBloc = DI().resolve<ProfileBloc>();
    final user = profileBloc.melodyzeUser;
    final userProfileImageUrl = user.profilePicUrl ?? Config.noUserDP;
    final username = 'FT. ${user.username.split(' ').first}';

    return MeloScaffold(
      showBackground: false,
      showBackButton: true,
      extendBody: true,
      onBackPressed: () => Navigator.of(context).pop(),
      secondaryAction: (context) => PopupMenuButton<String>(
        icon: ShaderMask(
          shaderCallback: (bounds) => AppGradients.gradientPinkIcon.createShader(bounds),
          child: const Icon(Icons.more_vert, size: 28, color: Colors.white),
        ),
        color: Colors.transparent,
        itemBuilder: (BuildContext context) => [
          PopupMenuItem(
            padding: EdgeInsets.zero,
            child: AppGradientContainer(
              gradient: AppGradients.gradientBlackTeal,
              child: Column(
                children: [
                  PopupMenuItem(
                    value: isPublishedRecording ? 'move_to_draft' : 'make_it_publish',
                    child: ListTile(
                      leading: const Icon(
                        Icons.check,
                        color: AppColors.white,
                      ),
                      title: Text(
                        isPublishedRecording ? 'Move to drafts' : 'Make it publish',
                        style: AppTextStyles.text16medium.copyWith(
                          fontFamily: AppFonts.iceland,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                    onTap: () async {
                      final result = await showYesNoDialog(context: context, title: isPublishedRecording ? 'Move to drafts' : 'Make it publish');
                      if (result && context.mounted) {
                        DI().resolve<ProfileBloc>().add(ProfileTogglePublishEvent(recording: widget.recording, publish: !isPublishedRecording));
                      }
                    },
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: ImageLoader.fromAsset(AssetPaths.gradientdivider),
                  ),
                  PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(
                        Icons.delete_forever,
                        color: AppColors.white,
                      ),
                      title: Text(
                        'Delete',
                        style: AppTextStyles.text16medium.copyWith(
                          fontFamily: AppFonts.iceland,
                          color: AppColors.white,
                        ),
                      ),
                      onTap: () async {
                        final result = await showYesNoDialog(context: context, title: 'Delete recording');
                        if (result && context.mounted) {
                          unawaited(_audioPlayer.stop());
                          Navigator.pop(context);
                          await Future.delayed(const Duration(milliseconds: 100));
                          if (context.mounted) {
                            DI().resolve<ProfileBloc>().add(DeleteRecordingEvent(widget.recording.id));
                            context.router.pop();
                          }
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      body: ColoredBox(
        color: Colors.black,
        child: Padding(
          padding: EdgeInsets.only(top: 58.0, bottom: 108.0),
          child: _lyricsLoaded
              ? _buildPlayerContent(context, lyricsList, userProfileImageUrl, username)
              : const Center(
                  child: AppCircularProgressIndicator(),
                ),
        ),
      ),
    );
  }

  Widget _buildPlayerContent(BuildContext context, List<String> lyrics, String userProfileImageUrl, String username) {
    return Stack(
      children: [
        // Main content - positioned naturally within the padded container
        Positioned.fill(
          child: Column(
            children: [
              // Header section with profile info and song metadata
              _buildHeaderSection(userProfileImageUrl, username),
              const SizedBox(height: 40),

              // Wave animation with centered play/pause button
              _buildWavePlayerSection(),

              // Lyrics section - moved closer to wave
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 30.0),
                  child: _LyricsDisplay(
                    lyrics: lyrics,
                    lyricsData: _lyricsData?.lyrics,
                    playbackPositionMillis: _playbackPositionMillis,
                    audioDurationMillis: _audioDurationMillis,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Centered play/pause button overlay with fade animation
        Positioned.fill(
          child: AutoFadeWidget(
            key: ValueKey(_isPlaying), // Force rebuild when playing state changes
            stopAutoFadeOnInteraction: !_isPlaying,
            initiallyVisible: true,
            fadeOutDelay: const Duration(seconds: 3), // Slightly longer delay
            child: Center(
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 250),
                transitionBuilder: (child, animation) {
                  return FadeTransition(
                    opacity: animation,
                    child: child,
                  );
                },
                child: IconButton(
                  key: ValueKey(_isPlaying ? 'pause' : 'play'),
                  icon: ImageLoader.fromAsset(
                    _isPlaying ? AssetPaths.pauseReel : AssetPaths.playReel,
                  ),
                  onPressed: _togglePlay,
                ),
              ),
            ),
          ),
        ),

        // Song details positioned at bottom (above nav bar)
        Positioned(
          bottom: 28,
          left: 20,
          right: 16,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showSongDetails)
                Row(
                  children: [
                    const SizedBox(width: 10),
                    Flexible(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Image.asset(
                                AssetPaths.musicNote,
                                width: 16,
                                height: 22,
                                color: AppColors.white,
                              ),
                              const SizedBox(width: 6),
                              SizedBox(
                                width: MediaQuery.sizeOf(context).width * 0.5,
                                child: Marqueee(
                                  text: "$songtitle · ${widget.recording.genre}",
                                  style: AppTextStyles.text16regular.copyWith(
                                    fontFamily: AppFonts.inter,
                                  ),
                                  width: 250,
                                ),
                              ),
                            ],
                          ),
                          Text(
                            "${Config.keyMapShort[widget.recording.scale] ?? widget.recording.scale} · ${widget.recording.tempo} bpm",
                            style: AppTextStyles.text14regular.copyWith(fontFamily: AppFonts.iceland),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),

        // App slider positioned at bottom
        Positioned(
          bottom: 0,
          width: MediaQuery.sizeOf(context).width,
          child: AppSlider(
            value: _playbackPositionMillis.toDouble(),
            max: _audioDurationMillis > 0 ? _audioDurationMillis.toDouble() : 100.0,
            onChanged: (value) {
              _audioPlayer.seek(Duration(milliseconds: value.toInt()));
            },
            gestureHitAreaHeight: 12,
            activeColor: AppColors.white,
            isRounded: false,
          ),
        ),
      ],
    );
  }

  Widget _buildHeaderSection(String userProfileImageUrl, String username) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(36.0, 65.0, 32.0, 28.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Profile image stack
          _buildProfileImage(userProfileImageUrl),
          const SizedBox(width: 20),
          // Recording info with song metadata
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        songtitle,
                        style: AppTextStyles.textEthnocentricStyle.copyWith(
                          fontSize: 18,
                          color: Colors.white,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.clip,
                      ),
                    ),
                  ],
                ),
                if (singer.isNotEmpty)
                  Text(
                    singer,
                    style: AppTextStyles.text16regular.copyWith(
                      fontFamily: AppFonts.iceland,
                      color: Colors.grey[400],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                const SizedBox(height: 10),
                Text(
                  username,
                  style: AppTextStyles.text18regular.copyWith(
                    fontFamily: AppFonts.iceland,
                    color: Colors.grey[400],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileImage(String userProfileImageUrl) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        SizedBox(
          width: 95,
          height: 95,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Image.asset(
                AssetPaths.profileBorder,
                fit: BoxFit.cover,
              ),
              ClipOval(
                child: SizedBox(
                  width: 90,
                  height: 90,
                  child: ImageLoader.network(
                    userProfileImageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[800],
                        child: const Icon(Icons.person, color: Colors.white, size: 48),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
        Positioned(
          right: -4, // Shift 4px more to the right
          bottom: -4, // Shift 4px more to the bottom
          child: SizedBox(
            width: 34,
            height: 34,
            child: Image.asset(
              AssetPaths.catComplete,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWavePlayerSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 6.0),
      child: SizedBox(
        height: 100,
        child: _isPlaying
            ? Image.asset(
                '${AssetPaths.gifPath}/wave_video.gif',
                fit: BoxFit.cover,
                width: double.infinity,
                height: 110,
              )
            : Image.asset(
                '${AssetPaths.pngPath}/wave_static.png',
                fit: BoxFit.cover,
                width: double.infinity,
                height: 110,
              ),
      ),
    );
  }
}

class _LyricsDisplay extends StatefulWidget {
  final List<String> lyrics;
  final LyricsList? lyricsData;
  final int playbackPositionMillis;
  final int audioDurationMillis;

  const _LyricsDisplay({
    required this.lyrics,
    required this.lyricsData,
    required this.playbackPositionMillis,
    required this.audioDurationMillis,
  });

  @override
  State<_LyricsDisplay> createState() => _LyricsDisplayState();
}

class _LyricsDisplayState extends State<_LyricsDisplay> {
  int _parseTimeToMillis(String time) {
    final parts = time.split(':');
    if (parts.length == 3) {
      final min = int.tryParse(parts[0]) ?? 0;
      final sec = int.tryParse(parts[1]) ?? 0;
      final ms = int.tryParse(parts[2]) ?? 0;
      return min * 60000 + sec * 1000 + ms;
    }
    return 0;
  }

  int _getCurrentLyricIndex() {
    if (widget.lyricsData == null || widget.lyrics.isEmpty) return 0;

    final data = widget.lyricsData!.data;
    if (data.isEmpty) return 0;

    // Handle looping: get position within the lyrics duration
    int adjustedPosition = widget.playbackPositionMillis;
    if (widget.audioDurationMillis > 0) {
      // Calculate lyrics duration
      final lastLyricEnd = data.isNotEmpty ? _parseTimeToMillis(data.last.endTime) : 0;
      final lyricsDuration = lastLyricEnd > 0 ? lastLyricEnd : widget.audioDurationMillis;

      // Get position within one loop cycle
      adjustedPosition = widget.playbackPositionMillis % lyricsDuration;
    }

    // Find current lyric based on timing
    for (int i = 0; i < data.length; i++) {
      final start = _parseTimeToMillis(data[i].startTime);
      final end = _parseTimeToMillis(data[i].endTime);

      if (end == 0) {
        // No end time specified, check if we're past start
        if (adjustedPosition >= start) {
          // Check if this is the last lyric or if next lyric hasn't started
          if (i == data.length - 1) return i;
          final nextStart = _parseTimeToMillis(data[i + 1].startTime);
          if (adjustedPosition < nextStart) return i;
        }
      } else {
        // End time specified
        if (adjustedPosition >= start && adjustedPosition < end) {
          return i;
        }
      }
    }

    // Default fallback
    return adjustedPosition < _parseTimeToMillis(data[0].startTime) ? 0 : data.length - 1;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.lyrics.isEmpty) {
      return Center(
        child: Text(
          'No lyrics available',
          style: AppTextStyles.text16regular.copyWith(
            color: Colors.grey,
          ),
        ),
      );
    }

    final currentIndex = _getCurrentLyricIndex();

    return SizedBox(
      height: 220,
      width: 240,
      child: Stack(
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildLyricLine(
                text: currentIndex > 0 ? widget.lyrics[currentIndex - 1] : '',
                isCurrent: false,
                isVisible: currentIndex > 0,
              ),

              // Current lyric (highlighted)
              _buildLyricLine(
                text: widget.lyrics[currentIndex],
                isCurrent: true,
                isVisible: true,
              ),

              // Next lyric
              _buildLyricLine(
                text: currentIndex < widget.lyrics.length - 1 ? widget.lyrics[currentIndex + 1] : '',
                isCurrent: false,
                isVisible: currentIndex < widget.lyrics.length - 1,
              ),
            ],
          ),

          if (currentIndex > 0)
            Positioned.fill(
              child: Align(
                alignment: Alignment.topCenter,
                child: Container(
                  margin: const EdgeInsets.only(top: 10),
                  height: 110,
                  child: Image.asset(
                    AssetPaths.mask1,
                    fit: BoxFit.cover,
                    width: double.infinity,
                  ),
                ),
              ),
            ),

          if (currentIndex < widget.lyrics.length - 1)
            Positioned.fill(
              child: Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  margin: const EdgeInsets.only(bottom: 20),
                  height: 100,
                  child: Image.asset(
                    AssetPaths.mask2,
                    fit: BoxFit.cover,
                    width: double.infinity,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLyricLine({
    required String text,
    required bool isCurrent,
    required bool isVisible,
  }) {
    if (!isVisible || text.isEmpty) {
      return const SizedBox(height: 72);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12.0), // Reduced vertical gap
      child: Center(
        child: Text(
          text,
          textAlign: TextAlign.center,
          style: AppTextStyles.textEthnocentricStyle.copyWith(
            fontSize: isCurrent ? 18 : 12,
            color: isCurrent ? const Color(0xFFEBC0E8) : Colors.white,
            fontWeight: FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
